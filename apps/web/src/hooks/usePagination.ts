import { useCallback, useEffect, useMemo } from "react";
import { useSearchParams } from "react-router-dom";

export const PAGE_SIZE_OPTIONS = [10, 20, 50, 100] as const;
export const PAGE_SIZE_ALL = "all" as const;
export type PageSizeOption = typeof PAGE_SIZE_OPTIONS[number] | typeof PAGE_SIZE_ALL;

// Helper functions for localStorage operations
const getStoredPageSize = (storageKey: string, defaultSize: PageSizeOption): PageSizeOption => {
  try {
    const stored = localStorage.getItem(storageKey);
    if (!stored) return defaultSize;

    if (stored === PAGE_SIZE_ALL) return PAGE_SIZE_ALL;

    const parsedSize = parseInt(stored, 10);
    if (PAGE_SIZE_OPTIONS.includes(parsedSize as any)) {
      return parsedSize as PageSizeOption;
    }

    return defaultSize;
  } catch (error) {
    console.warn('Failed to read page size from localStorage:', error);
    return defaultSize;
  }
};

const setStoredPageSize = (storageKey: string, pageSize: PageSizeOption): void => {
  try {
    localStorage.setItem(storageKey, pageSize.toString());
  } catch (error) {
    console.warn('Failed to save page size to localStorage:', error);
  }
};

interface UsePaginationOptions {
  totalCount: number;
  pageParamName?: string;
  pageSizeParamName?: string;
  allowPageSizeChange?: boolean;
  defaultPageSize?: PageSizeOption;
  storageKey?: string; // Key for localStorage persistence
}

interface UsePaginationReturn {
  currentPage: number;
  totalPages: number;
  offset: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  pageSize: PageSizeOption;
  itemsPerPage: number;
  goToPage: (page: number) => void;
  goToNextPage: () => void;
  goToPrevPage: () => void;
  goToFirstPage: () => void;
  goToLastPage: () => void;
  setPageSize: (size: PageSizeOption) => void;
}

export function usePagination({
  totalCount,
  pageParamName = "page",
  pageSizeParamName = "pageSize",
  allowPageSizeChange = false,
  defaultPageSize = 10,
  storageKey,
}: UsePaginationOptions): UsePaginationReturn {
  const [searchParams, setSearchParams] = useSearchParams();

  // Get current page size from URL parameters, localStorage, or default
  const pageSize = useMemo(() => {
    if (!allowPageSizeChange) return defaultPageSize;

    // First priority: URL parameter
    const pageSizeParam = searchParams.get(pageSizeParamName);
    if (pageSizeParam) {
      if (pageSizeParam === PAGE_SIZE_ALL) return PAGE_SIZE_ALL;

      const parsedSize = parseInt(pageSizeParam, 10);
      if (PAGE_SIZE_OPTIONS.includes(parsedSize as any)) {
        return parsedSize as PageSizeOption;
      }
    }

    // Second priority: localStorage (if storageKey is provided)
    if (storageKey) {
      return getStoredPageSize(storageKey, defaultPageSize);
    }

    // Fallback: default
    return defaultPageSize;
  }, [searchParams, pageSizeParamName, allowPageSizeChange, defaultPageSize, storageKey]);

  // Calculate actual items per page (for "all" option)
  const actualItemsPerPage = useMemo(() => {
    if (pageSize === PAGE_SIZE_ALL) return totalCount || 1;
    return pageSize as number;
  }, [pageSize, totalCount]);

  // Get current page from URL parameters
  const currentPage = useMemo(() => {
    const pageParam = searchParams.get(pageParamName);
    return Math.max(1, parseInt(pageParam || "1", 10));
  }, [searchParams, pageParamName]);

  // Calculate pagination values
  const totalPages = useMemo(() => {
    if (pageSize === PAGE_SIZE_ALL) return 1;
    return Math.ceil(totalCount / actualItemsPerPage);
  }, [totalCount, actualItemsPerPage, pageSize]);

  const offset = useMemo(() => {
    if (pageSize === PAGE_SIZE_ALL) return 0;
    return (currentPage - 1) * actualItemsPerPage;
  }, [currentPage, actualItemsPerPage, pageSize]);

  const hasNextPage = useMemo(() => {
    return currentPage < totalPages;
  }, [currentPage, totalPages]);

  const hasPrevPage = useMemo(() => {
    return currentPage > 1;
  }, [currentPage]);

  // Update URL with new page
  const updatePage = useCallback(
    (page: number) => {
      const newParams = new URLSearchParams(searchParams);
      if (page === 1) {
        newParams.delete(pageParamName); // Remove page param for page 1 (cleaner URLs)
      } else {
        newParams.set(pageParamName, page.toString());
      }
      setSearchParams(newParams);
    },
    [searchParams, setSearchParams, pageParamName]
  );

  // Update URL with new page size and preserve scroll position by calculating appropriate page
  const setPageSize = useCallback(
    (size: PageSizeOption) => {
      if (!allowPageSizeChange) return;

      // Capture current scroll position before URL change
      const currentScrollY = window.scrollY;

      // Save to localStorage if storageKey is provided
      if (storageKey) {
        setStoredPageSize(storageKey, size);
      }

      const newParams = new URLSearchParams(searchParams);

      // Set page size in URL (only if different from stored preference)
      const storedSize = storageKey ? getStoredPageSize(storageKey, defaultPageSize) : defaultPageSize;
      if (size === storedSize && !searchParams.get(pageSizeParamName)) {
        // If setting to stored preference and no URL param exists, don't add URL param
        newParams.delete(pageSizeParamName);
      } else if (size === defaultPageSize && !storageKey) {
        // If no localStorage and setting to default, remove URL param
        newParams.delete(pageSizeParamName);
      } else {
        // Otherwise, set the URL param
        newParams.set(pageSizeParamName, size.toString());
      }

      // Calculate which page to go to based on current position to preserve scroll position
      if (size === PAGE_SIZE_ALL) {
        // If switching to "all", go to page 1
        newParams.delete(pageParamName);
      } else {
        // Calculate the new page based on current offset to maintain position
        const currentOffset = (currentPage - 1) * actualItemsPerPage;
        const newItemsPerPage = size as number;
        const newPage = Math.floor(currentOffset / newItemsPerPage) + 1;

        // Ensure the new page is valid (at least 1)
        const targetPage = Math.max(1, newPage);

        if (targetPage === 1) {
          newParams.delete(pageParamName); // Remove page param for page 1 (cleaner URLs)
        } else {
          newParams.set(pageParamName, targetPage.toString());
        }
      }

      // Use replace: true to prevent scroll-to-top behavior
      setSearchParams(newParams, { replace: true });

      // Restore scroll position after a brief delay to ensure DOM has updated
      setTimeout(() => {
        window.scrollTo(0, currentScrollY);
      }, 0);
    },
    [searchParams, setSearchParams, pageSizeParamName, pageParamName, allowPageSizeChange, defaultPageSize, storageKey, currentPage, actualItemsPerPage]
  );

  // Navigation functions
  const goToPage = useCallback(
    (page: number) => {
      if (page >= 1 && page <= totalPages) {
        updatePage(page);
      }
    },
    [updatePage, totalPages]
  );

  const goToNextPage = useCallback(() => {
    if (hasNextPage) {
      updatePage(currentPage + 1);
    }
  }, [hasNextPage, updatePage, currentPage]);

  const goToPrevPage = useCallback(() => {
    if (hasPrevPage) {
      updatePage(currentPage - 1);
    }
  }, [hasPrevPage, updatePage, currentPage]);

  const goToFirstPage = useCallback(() => {
    updatePage(1);
  }, [updatePage]);

  const goToLastPage = useCallback(() => {
    updatePage(totalPages);
  }, [updatePage, totalPages]);

  // Sync current page size to localStorage on mount (if not already stored)
  useEffect(() => {
    if (storageKey && allowPageSizeChange) {
      const stored = getStoredPageSize(storageKey, defaultPageSize);
      // If no stored preference exists, save the current page size
      if (stored === defaultPageSize && pageSize !== defaultPageSize) {
        setStoredPageSize(storageKey, pageSize);
      }
    }
  }, [storageKey, allowPageSizeChange, defaultPageSize, pageSize]);

  return {
    currentPage,
    totalPages,
    offset,
    hasNextPage,
    hasPrevPage,
    pageSize,
    itemsPerPage: actualItemsPerPage,
    goToPage,
    goToNextPage,
    goToPrevPage,
    goToFirstPage,
    goToLastPage,
    setPageSize,
  };
}
