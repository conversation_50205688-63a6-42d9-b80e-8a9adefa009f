import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Appointment } from "@/hooks/dashboard/useAppointments";
import {
    Calendar,
    CalendarCheck,
    CheckCircle,
    Clock
} from "lucide-react";

interface AppointmentStatsProps {
  appointments: Appointment[];
  isLoading?: boolean;
}

export function AppointmentStats({ appointments, isLoading }: AppointmentStatsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted animate-pulse rounded" />
              </CardTitle>
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted animate-pulse rounded mb-1" />
              <div className="h-3 bg-muted animate-pulse rounded w-3/4" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Calculate stats from appointments
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const stats = {
    total: appointments.length,
    scheduled: appointments.filter(apt => apt.status === "scheduled").length,
    completed: appointments.filter(apt => apt.status === "completed").length,
    cancelled: appointments.filter(apt => apt.status === "cancelled").length,
    noShow: appointments.filter(apt => apt.status === "no_show").length,
    todayCount: appointments.filter(apt => {
      const aptDate = new Date(apt.appointment_date);
      return aptDate >= today && aptDate < tomorrow;
    }).length,
    upcomingCount: appointments.filter(apt => {
      const aptDate = new Date(apt.appointment_date);
      return aptDate >= today && (apt.status === "scheduled" || apt.status === "checked_in");
    }).length,
    inProgress: appointments.filter(apt =>
      apt.status === "in_progress" || apt.status === "checked_in"
    ).length,
  };

  const statCards = [
    {
      title: "Total Appointments",
      value: stats.total.toLocaleString(),
      description: `${stats.scheduled} scheduled, ${stats.completed} completed`,
      icon: Calendar,
      trend: "neutral" as const,
    },
    {
      title: "Today's Appointments",
      value: stats.todayCount.toLocaleString(),
      description: `${stats.inProgress} currently in progress`,
      icon: CalendarCheck,
      trend: stats.todayCount > 0 ? "up" : "neutral" as const,
    },
    {
      title: "Upcoming Scheduled",
      value: stats.upcomingCount.toLocaleString(),
      description: "Future appointments",
      icon: Clock,
      trend: "neutral" as const,
    },
    {
      title: "Completion Rate",
      value: stats.total > 0 ? `${Math.round((stats.completed / stats.total) * 100)}%` : "0%",
      description: `${stats.cancelled + stats.noShow} cancelled/no-show`,
      icon: CheckCircle,
      trend: stats.total > 0 && (stats.completed / stats.total) > 0.8 ? "up" : "neutral" as const,
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
