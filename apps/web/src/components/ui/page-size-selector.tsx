
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { PAGE_SIZE_ALL, PAGE_SIZE_OPTIONS, type PageSizeOption } from "@/hooks/usePagination";
import { cn } from "@/lib/utils";

interface PageSizeSelectorProps {
  pageSize: PageSizeOption;
  onPageSizeChange: (size: PageSizeOption) => void;
  totalCount: number;
  className?: string;
  showLabel?: boolean;
  size?: "sm" | "default";
}

export function PageSizeSelector({
  pageSize,
  onPageSizeChange,
  totalCount,
  className,
  showLabel = true,
  size = "default",
}: PageSizeSelectorProps) {
  const formatPageSizeLabel = (size: PageSizeOption) => {
    if (size === PAGE_SIZE_ALL) {
      return `All (${totalCount})`;
    }
    return size.toString();
  };

  const formatCurrentValue = (size: PageSizeOption) => {
    if (size === PAGE_SIZE_ALL) {
      return `All (${totalCount})`;
    }
    return `${size} per page`;
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      {showLabel && (
        <span className="text-sm text-muted-foreground whitespace-nowrap">
          Show:
        </span>
      )}
      <Select
        value={pageSize.toString()}
        onValueChange={(value) => {
          const newSize = value === PAGE_SIZE_ALL ? PAGE_SIZE_ALL : parseInt(value, 10) as PageSizeOption;
          onPageSizeChange(newSize);
        }}
      >
        <SelectTrigger className={cn("w-auto min-w-[120px]", size === "sm" && "h-8")} size={size}>
          <SelectValue>
            {formatCurrentValue(pageSize)}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {PAGE_SIZE_OPTIONS.map((option) => (
            <SelectItem key={option} value={option.toString()}>
              {formatPageSizeLabel(option)}
            </SelectItem>
          ))}
          {totalCount > Math.max(...PAGE_SIZE_OPTIONS) && (
            <SelectItem value={PAGE_SIZE_ALL}>
              {formatPageSizeLabel(PAGE_SIZE_ALL)}
            </SelectItem>
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
