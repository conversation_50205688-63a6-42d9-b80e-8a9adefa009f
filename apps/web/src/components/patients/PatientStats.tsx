import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { PatientWithStats } from "@/types/patient";
import { Activity, Calendar, TrendingUp, Users } from "lucide-react";

interface PatientStatsProps {
  patients: PatientWithStats[];
  totalCount: number;
  isLoading?: boolean;
}

export function PatientStats({ patients, totalCount, isLoading }: PatientStatsProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted animate-pulse rounded" />
              </CardTitle>
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted animate-pulse rounded mb-1" />
              <div className="h-3 bg-muted animate-pulse rounded w-3/4" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  // Calculate stats from current page patients and total count
  const today = new Date();
  const thirtyDaysAgo = new Date(today);
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  // Stats from current page (for percentages and averages)
  const currentPageStats = {
    active: patients.filter(p => p.status === "active").length,
    inactive: patients.filter(p => p.status === "inactive").length,
    new: patients.filter(p => p.status === "new").length,
    recentlyAdded: patients.filter(p => {
      if (!p.created_at) return false;
      const createdAt = new Date(p.created_at);
      return createdAt >= thirtyDaysAgo;
    }).length,
    withUpcomingAppointments: patients.filter(p => p.next_appointment).length,
    averageAge: patients.length > 0 ? patients.reduce((sum, p) => sum + p.age, 0) / patients.length : 0,
  };

  // Estimate percentages based on current page (this is an approximation)
  const activePercentage = patients.length > 0 ? Math.round((currentPageStats.active / patients.length) * 100) : 0;
  const upcomingPercentage = patients.length > 0 ? Math.round((currentPageStats.withUpcomingAppointments / patients.length) * 100) : 0;

  const statCards = [
    {
      title: "Total Patients",
      value: totalCount.toLocaleString(),
      description: `${currentPageStats.recentlyAdded} added recently`,
      icon: Users,
      trend: currentPageStats.recentlyAdded > 0 ? "up" : "neutral",
    },
    {
      title: "Active Patients",
      value: currentPageStats.active.toLocaleString(),
      description: `~${activePercentage}% of current page`,
      icon: Activity,
      trend: currentPageStats.active > currentPageStats.inactive ? "up" : "down",
    },
    {
      title: "Upcoming Appointments",
      value: currentPageStats.withUpcomingAppointments.toLocaleString(),
      description: `~${upcomingPercentage}% have appointments`,
      icon: Calendar,
      trend: "neutral",
    },
    {
      title: "Average Age",
      value: Math.round(currentPageStats.averageAge).toString(),
      description: "Years old (current page)",
      icon: TrendingUp,
      trend: "neutral",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
